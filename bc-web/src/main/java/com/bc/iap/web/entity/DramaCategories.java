package com.bc.iap.web.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 短剧分类关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-05
 */
@Getter
@Setter
@TableName("drama_categories")
public class DramaCategories implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 短剧ID
     */
    private String dramaId;

    /**
     * 分类ID
     */
    private Integer categoryId;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
}
