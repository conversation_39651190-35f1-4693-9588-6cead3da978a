package com.bc.iap.web.common.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;

@Data
@Accessors(chain = true)
public class PageData<T> {

	/**当前页*/
	private int pageNum;

	/**每页的数量 */
	private int pageSize;

	/**总记录数*/
	private long total;

	/**总页数*/
	private int pages;

	/**页大小*/
	private int size;

	private List<T> list = new ArrayList<>();

	public PageData(){}

	public PageData(int pageNum, int pageSize, long total, int pages, int size, List<T> list) {
		this.pageNum = pageNum;
		this.pageSize = pageSize;
		this.total = total;
		this.pages = pages;
		this.size = size;
		this.list = list;
	}
}
