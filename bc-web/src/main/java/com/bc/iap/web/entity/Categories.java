package com.bc.iap.web.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 短剧分类表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-05
 */
@Getter
@Setter
public class Categories implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 分类唯一标识符
     */
    @TableId(value = "category_id", type = IdType.AUTO)
    private Integer categoryId;

    /**
     * 本版本语言代码
     */
    private String language;

    /**
     * 分类名称
     */
    private String name;

    /**
     * 分类描述
     */
    private String description;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 是否发布，默认1，0为未发布
     */
    private Byte status;
}
