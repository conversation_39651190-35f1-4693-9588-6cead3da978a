package com.bc.iap.web.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 剧集表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-05
 */
@Getter
@Setter
public class Episodes implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "episode_id", type = IdType.AUTO)
    private Integer episodeId;

    /**
     * 关联短剧ID
     */
    private Integer dramaId;

    /**
     * 剧集序号
     */
    private Integer episodeNumber;

    /**
     * 季号
     */
    private Integer seasonNumber;

    /**
     * 剧集标题
     */
    private String title;

    /**
     * 剧集描述
     */
    private String description;

    /**
     * 剧集发布日期
     */
    private LocalDateTime releaseDate;

    /**
     * 剧集时长(秒)
     */
    private Integer duration;

    /**
     * 视频链接
     */
    private String videoUrl;

    /**
     * 文件大小(字节)
     */
    private Long fileSize;

    /**
     * 文件格式
     */
    private String fileFormat;

    /**
     * 剧集缩略图
     */
    private String thumbnailUrl;

    /**
     * 剧集状态
     */
    private String status;

    /**
     * 观看次数
     */
    private Integer viewsCount;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;

    /**
     * 分类备注
     */
    private String remk;

    /**
     * 字幕链接
     */
    private String wordUrl;
}
