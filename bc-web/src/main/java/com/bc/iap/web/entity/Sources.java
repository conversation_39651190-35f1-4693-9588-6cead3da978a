package com.bc.iap.web.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 短剧来源表
 * </p>
 *`
 * <AUTHOR>
 * @since 2025-09-05
 */
@Getter
@Setter
public class Sources implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "source_id", type = IdType.AUTO)
    private Integer sourceId;

    /**
     * 来源名称
     */
    private String name;

    /**
     * 类型:合作方/嫁接/用户上传
     */
    private String type;

    /**
     * 来源描述
     */
    private String description;

    /**
     * 联系方式
     */
    private String contactInfo;

    /**
     * 来源方网站
     */
    private String website;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;
}
