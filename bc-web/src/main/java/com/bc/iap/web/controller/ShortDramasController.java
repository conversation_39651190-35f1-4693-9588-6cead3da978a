package com.bc.iap.web.controller;

import com.batmobi.dataxsync.common.model.BaseResponse;
import com.bc.iap.web.common.dto.EpisodesDetailReq;
import com.bc.iap.web.common.dto.EpisodesReq;
import com.bc.iap.web.common.dto.HomepageReq;
import com.bc.iap.web.common.vo.EpisodesDetailVo;
import com.bc.iap.web.common.vo.EpisodesVo;
import com.bc.iap.web.common.vo.HomepageVo;
import com.bc.iap.web.service.IShortDramasService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 短剧相关接口控制器
 *
 * 提供短剧首页数据、剧集信息、剧集详情等功能
 *
 * <AUTHOR>
 * @since 2025-09-05
 */
@Slf4j
@Validated
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/v1/short-dramas")
public class ShortDramasController {
    private final IShortDramasService service;

    /**
     * 获取首页数据
     *
     * 返回按分类分组的短剧列表，用于首页展示
     *
     * @param req 首页请求参数，包含站点域名等信息
     * @return 包含分类短剧列表的首页数据
     */
    @PostMapping("/homepage")
    public BaseResponse<HomepageVo> getHomepage(@Valid @RequestBody HomepageReq req) {
        log.info("获取首页数据请求: {}", req);
        HomepageVo result = service.homepage(req);
        log.info("首页数据获取成功，返回{}个分类", result.getPlateList() != null ? result.getPlateList().size() : 0);
        return BaseResponse.success(result);
    }

    /**
     * 根据短剧MD5ID查询剧集信息
     *
     * 获取指定短剧的基本信息和剧集总数
     *
     * @param req 包含短剧MD5ID的请求参数
     * @return 短剧的剧集基本信息（标题、总集数等）
     */
    @PostMapping("/episodes")
    public BaseResponse<EpisodesVo> getEpisodes(@Valid @RequestBody EpisodesReq req) {
        log.info("查询剧集信息请求: md5Id={}", req.getMd5Id());
        EpisodesVo result = service.episodesInfo(req);
        log.info("剧集信息查询成功: title={}, totalCount={}", result.getTitle(), result.getTotalCount());
        return BaseResponse.success(result);
    }

    /**
     * 根据剧集ID和集数序号查询剧集详情
     *
     * 获取指定剧集的详细信息，包括视频链接、时长等
     *
     * @param req 包含剧集ID和集数序号的请求参数
     * @return 剧集详细信息（视频链接、时长、观看次数等）
     */
    @PostMapping("/detail")
    public BaseResponse<EpisodesDetailVo> getEpisodeDetail(@Valid @RequestBody EpisodesDetailReq req) {
        log.info("查询剧集详情请求: dramaId={}, index={}", req.getDramaId(), req.getIndex());
        EpisodesDetailVo result = service.episodeDetailInfo(req);
        log.info("剧集详情查询成功: episodeId={}, title={}", result.getEpisodeId(), result.getTitle());
        return BaseResponse.success(result);
    }
}
