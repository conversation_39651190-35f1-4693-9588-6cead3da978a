package com.bc.iap.web.common.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
public class PageRequest implements Serializable {

    private static final long serialVersionUID = -1771705466572683521L;

    /**
     *  当前页
     */
    private int pageNum = 1;

    /**
     *  每页的数量
     */
    private int pageSize = 10;


}
