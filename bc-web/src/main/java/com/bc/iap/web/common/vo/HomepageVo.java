package com.bc.iap.web.common.vo;

import lombok.Data;

import java.util.List;

@Data
public class HomepageVo {
    private String siteDomain;
    private List<Plate> plateList;

    @Data
    public static class Plate {
        private String plateName;
        private List<PlateList> plateList;
    }

    @Data
    public static class PlateList {
        private String md5Id;
        private String title;
        private String thumbnailUrl;

        public PlateList(String dramaId, String title, String thumbnailUrl) {
            this.md5Id = dramaId;
            this.title = title;
            this.thumbnailUrl = thumbnailUrl;
        }
    }



}

